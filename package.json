{"name": "flawlessaest<PERSON><PERSON><PERSON><PERSON><PERSON>", "private": true, "type": "module", "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix"}, "dependencies": {"@fontsource/roboto": "5.2.5", "@mdi/font": "7.4.47", "vue": "^3.5.13", "vuetify": "^3.8.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "eslint": "^9.23.0", "eslint-config-vuetify": "^4.0.0", "globals": "^16.0.0", "sass-embedded": "^1.86.3", "unplugin-fonts": "^1.3.1", "unplugin-vue-components": "^28.4.1", "unplugin-vue-router": "^0.12.0", "vite": "^6.2.2", "vite-plugin-vuetify": "^2.1.1", "vue-router": "^4.5.0"}, "packageManager": "pnpm@10.12.1+sha512.f0dda8580f0ee9481c5c79a1d927b9164f2c478e90992ad268bbb2465a736984391d6333d2c327913578b2804af33474ca554ba29c04a8b13060a717675ae3ac"}